{"name": "urbanvenue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.17.1", "axios": "^1.7.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "install": "^0.13.0", "jsdom": "^25.0.1", "jspdf": "^3.0.1", "moment": "^2.30.1", "npm": "^11.0.0", "react": "^18.3.1", "react-big-calendar": "^1.13.2", "react-day-picker": "^9.0.8", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-pdf": "^9.1.1", "react-router-dom": "^6.26.0", "react-to-pdf": "^1.0.1", "react-to-print": "^3.0.5"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.8.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.9", "vite": "^5.4.0"}}
import React, { useEffect, useState } from "react";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import html2pdf from "html2pdf.js"; // Optional if using html2pdf
import HomeIcon from "@mui/icons-material/Home";
import invoLogo from "../assets/Invo-Logo.png";
import callIcon from "../assets/call.png";
import emailIcon from "../assets/email.png";
import instagramIcon from "../assets/instagram.png";
import facebookIcon from "../assets/facebook.png";
import CloudDownloadIcon from "@mui/icons-material/CloudDownload";
import Qr from "../assets/Invoice-Logo.png";
import { useLocation, useNavigate } from "react-router-dom";
import { DASHBOARD_ROUTE } from "../routes/Routes";
import { CircularProgress } from "@mui/material"; // NEW: Added loading spinner
const InvoiceCopy = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const formData = location.state;

  //const targetRefpic = useRef()
  const [imageExists, setImageExists] = useState(false);
  const [base64Images, setBase64Images] = useState([]);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // const downloadPDF = () => {
  //   const element = document.getElementById("pdf-content");

  //   const options = {
  //     margin: 0,
  //     filename: "invoice.pdf",
  //     image: { type: "jpeg", quality: 0.98 },
  //     html2canvas: { scale: 1.5 },
  //     jsPDF: { unit: "in", format: "a4", orientation: "portrait" },

  //   };

  //   // Use html2pdf to convert div content to a PDF
  //   html2pdf().from(element).set(options).save();
  // };

  const downloadPDF = async () => {
    if (isGeneratingPDF) return;
    setIsGeneratingPDF(true);

    try {
      const element = document.getElementById("pdf-content");
      const doc = new jsPDF({
        orientation: "p",
        unit: "pt",
        format: "a4",
        hotfixes: ["px_scaling"],
      });

      const options = {
        margin: 0,
        html2canvas: {
          scale: 0.75,
          // letterRendering: false,
          width: 400,
          logging: true,
          useCORS: true,
          // letterRendering: true,
        },
        pagebreak: { mode: 'css' },
      };

      await doc.html(element, options);

      doc.save(`${formData.bookingId}.pdf`);
    } catch (error) {
      console.error("PDF generation error:", error);
      alert("Error generating PDF. Please try again.");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // useEffect(() => {
  //   window.scrollTo(0, 0);

  //   if (formData.photo && Array.isArray(formData.photo)) {
  //     const imageUrls = formData.photo.map(
  //       (filename) => `${import.meta.env.VITE_BACKEND_URL}${filename}`
  //     );

  //     // Fetch and convert images to Base64
  //     Promise.all(
  //       imageUrls.map(async (url) => {
  //         try {
  //           const response = await fetch(url);
  //           if (!response.ok) throw new Error("Image not found");
  //           const blob = await response.blob();

  //           return new Promise((resolve) => {
  //             const reader = new FileReader();
  //             reader.onloadend = () => resolve(reader.result);
  //             reader.readAsDataURL(blob);
  //           });
  //         } catch (error) {
  //           console.error("Error loading image:", error);
  //           return null; // Skip if there's an error
  //         }
  //       })
  //     ).then((base64Results) => {
  //       const filteredImages = base64Results.filter((img) => img !== null);
  //       setBase64Images(filteredImages);
  //     });
  //   }
  // }, [formData.photo]);

  useEffect(() => {
    window.scrollTo(0, 0);

    if (formData.photo && Array.isArray(formData.photo)) {
      const imageUrls = formData.photo.map(
        (filename) => `${import.meta.env.VITE_BACKEND_URL}${filename}`
      );

      Promise.all(
        imageUrls.map(async (url) => {
          try {
            const response = await fetch(url);
            const blob = await response.blob();
            return new Promise((resolve) => {
              const reader = new FileReader();
              reader.onloadend = () => resolve(reader.result);
              reader.readAsDataURL(blob);
            });
          } catch (error) {
            console.error("Error loading image:", error);
            return null;
          }
        })
      ).then((base64Results) => {
        const filteredImages = base64Results.filter((img) => img !== null);
        setBase64Images(filteredImages);
      });
    }
  }, [formData.photo]);

  useEffect(() => {
    if (imageExists && formData.photo) {
      const imageUrl = `${import.meta.env.VITE_BACKEND_URL}${formData.photo}`;
      console.log("===>>>> Image URL:", imageUrl);

      fetch(imageUrl)
        .then((response) => response.blob())
        .then((blob) => {
          const reader = new FileReader();
          reader.onloadend = () => setBase64Image(reader.result);
          reader.readAsDataURL(blob);
        })
        .catch((error) =>
          console.error("Error converting image to Base64:", error)
        );
    }
  }, [imageExists, formData.photo]);

  const Home = () => {
    navigate(DASHBOARD_ROUTE);
  };

  return (
    <div className="flex flex-col items-center bg-red-50 py-8">
      {/* Invoice Div */}

      <div className="flex mb-10">
        <button
          onClick={() => Home()}
          className=" mx-1 button text-white flex "
        >
          <HomeIcon className="mr-1" />
          Home
        </button>
        {/* 
        <button
          onClick={downloadPDF}
          className=" mx-1 button text-white flex justify-center items-center "
        >
          <CloudDownloadIcon className="mr-1" />
          Download
        </button> */}

        <button
          onClick={downloadPDF}
          disabled={isGeneratingPDF}
          className="mx-1 button text-white flex justify-center items-center"
        >
          {isGeneratingPDF ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            <>
              <CloudDownloadIcon className="mr-1" />
              Download
            </>
          )}
        </button>
      </div>

      <div
        id="pdf-content"
        className="w-[794px] bg-white border-x-4 border-red-600 shadow-lg print:w-full"
      >
        <div className="h-4 flex print:h-4">
          <div className="flex-1 bg-black"></div>
          <div className="flex-1 bg-red-500"></div>
          <div className="flex-1 bg-black"></div>
        </div>
        <div className="pl-8 pr-8 pt-5">
          {/* Logo and Header */}
          <div className="flex flex-col items-center my-2">
            {/* <img src="path-to-your-logo" alt="Logo" className="mb-4" /> */}
            <img
              src={invoLogo} // Replace this with your logo
              alt="Logo"
              className="mb-2 h-[60px] "
            />
            <h1 className="text-l block text-center text-green-500 font-bold tracking-normal">
              Your&nbsp;&nbsp;booking&nbsp;&nbsp;has&nbsp;&nbsp;been&nbsp;&nbsp;confirmed
            </h1>
          </div>

          {/* Form Sections */}
          <form className="space-y-6 mt-10 bg-white">
            {/* Your form content */}
            <h4 className="text-l font-bold">Voucher&nbsp;&nbsp;Details</h4>

            <div className="w-full grid grid-cols-3 gap-6 bg-white">
              <div className="flex flex-col gap-2">
                <label className="block text-gray-700 mb-1 font-bold text-sm">
                  Voucher&nbsp;&nbsp;ID
                </label>
                <input
                  type="text"
                  value={formData.bookingId}
                  className="border border-gray-300 w-full pl-3 h-10  text-sm rounded text-start leading-normal  appearance-none"
                  readOnly
                />
              </div>
              <div className="flex flex-col gap-2">
                <label className="block text-gray-700 mb-1 font-bold text-sm">
                  Farmhouse&nbsp;&nbsp;Name
                </label>
                <input
                  type="text"
                  value={formData.venue}
                  className="border border-gray-300 w-full pl-3 h-10  text-sm rounded text-start leading-normal  appearance-none"
                  readOnly
                />
              </div>
              <div className="flex flex-col gap-2">
                <label className="block text-gray-700 mb-1 font-bold text-sm">
                  Guest&nbsp;&nbsp;Name
                </label>
                <input
                  type="text"
                  value={formData.guestName}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  readOnly
                />
              </div>
            </div>

            {/* Phone Number & Email */}

            <div className="w-full grid grid-cols-3 gap-6 bg-white">
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Phone Number
                </label>
                <input
                  type="text"
                  value={formData.phoneNumber}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  // defaultValue="(*************"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Email
                </label>
                <input
                  type="email"
                  value={formData.email}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  // defaultValue="<EMAIL>"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Occasion
                </label>
                <input
                  type="text"
                  value={formData.occasion}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  // defaultValue="<EMAIL>"
                  readOnly
                />
              </div>
            </div>

            <div className="w-full grid grid-cols-2  gap-6">
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Check In - Date
                </label>
                <input
                  type="text"
                  value={
                    new Date(formData.checkInDate).toLocaleDateString("en-GB") // Formats as DD-MM-YYYY
                  }
                  className="border border-gray-300 w-full pl-3 h-10 rounded text-sm"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Check In Time
                </label>
                <input
                  type="text"
                  value={new Date(
                    `1970-01-01T${formData.checkInTime}`
                  ).toLocaleTimeString("en-US", {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true, // Converts to AM/PM
                  })}
                  className="border border-gray-300 w-full pl-3 h-10 rounded text-sm"
                  readOnly
                />
              </div>
            </div>

            {/* CheckOut Details */}
            <div className="w-full grid grid-cols-2  gap-6">
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Checkout - Date
                </label>
                <input
                  type="text"
                  value={
                    new Date(formData.checkOutDate).toLocaleDateString("en-GB") // Formats as DD-MM-YYYY
                  }
                  className="border border-gray-300 w-full pl-3 h-10 rounded text-sm"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Checkout Time
                </label>
                <input
                  type="text"
                  value={new Date(
                    `1970-01-01T${formData.checkOutTime}`
                  ).toLocaleTimeString("en-US", {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true, // Converts to AM/PM
                  })}
                  className="border border-gray-300 w-full pl-3 h-10 rounded text-sm"
                  readOnly
                />
              </div>
            </div>

            {/* Other sections */}
            <h4 className="text-l font-bold">Booking&nbsp;&nbsp;Partner</h4>
            <div className="w-full grid grid-cols-2 gap-6 bg-white">
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Booking&nbsp;&nbsp;Partner Name
                </label>
                <input
                  type="text"
                  value={formData.bookingPartnerName}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded  text-sm"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Booking&nbsp;&nbsp;Partner Phone No
                </label>
                <input
                  type="text"
                  value={formData.bookingPartnerPhoneNumber}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded  text-sm"
                  readOnly
                />
              </div>
            </div>

            <div className="w-full grid grid-cols-2  gap-6">
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Number&nbsp;&nbsp;of Adults
                </label>
                <input
                  type="text"
                  value={formData.numberOfAdults}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  // defaultValue={2}
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Number&nbsp;&nbsp;of Kids
                </label>
                <input
                  type="text"
                  value={formData.numberOfKids}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  // defaultValue={1}
                  readOnly
                />
              </div>
            </div>

            {/* <div className="pdf-page-break"></div> */}

            <div className="w-full grid grid-cols-2  gap-6">
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Property&nbsp;&nbsp;Host Name
                </label>
                <input
                  type="text"
                  value={formData.hostOwnerName}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  // defaultValue="980000000"
                  readOnly
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 mb-2 font-bold  text-sm">
                    Property&nbsp;&nbsp;Host Number
                  </label>
                  <input
                    type="text"
                    value={formData.hostNumber}
                    className="border border-gray-300 w-full  pl-3 h-10 rounded  text-sm"
                    // defaultValue="Akshat"
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* Payment Details */}
            <div className="w-full grid grid-cols-3  gap-6">
              <div>
                <label className="block text-gray-700 mb-2 font-bold  text-sm">
                  Total booking&nbsp;&nbsp;amount
                </label>
                <input
                  type="text"
                  value={formData.totalBooking}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded  text-sm"
                  // defaultValue="1,00,000"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold  text-sm">
                  Farm Tariff
                </label>
                <input
                  type="text"
                  value={formData.farmTref}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded  text-sm"
                  // defaultValue="10,000"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold  text-sm">
                  Advance&nbsp;&nbsp;Payment
                </label>
                <input
                  type="text"
                  value={formData.advance}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded  text-sm"
                  // defaultValue="50,000"
                  readOnly
                />
              </div>
            </div>
            <div className="pdf-page-break"></div>

            <div className="w-full grid grid-cols-2  gap-6">
              {/* <div>
                <label className="block text-gray-700 mb-2 font-bold">Advance payment </label>
                <input
                  type="text"
                  value={formData.advance}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded"
                  readOnly
                />
              </div> */}
              {/* <div>
                <label className="block text-gray-700 mb-2 font-bold">Advance Collected by </label>
                <input
                  type="text"
                  value={formData.advanceCollectedBy}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded"
                  // defaultValue="50,000"
                  readOnly
                />
              </div> */}

              {/* <div>
                <label className="block text-gray-700 mb-2">
                  Advance Collected By
                </label>
                <input
                  type="text"
                  value={formData.advanceCollectedBy}
                  className="border border-gray-300 w-full p-2 rounded"
                  // defaultValue="50,000"
                  readOnly
                />
              </div> */}

              <div>
                <label className="block text-gray-700 mb-2 font-bold  text-sm">
                  Balance&nbsp;&nbsp;Payment
                </label>
                <input
                  type="text"
                  value={formData.balancePayment}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded  text-sm"
                  // defaultValue="50,000"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-gray-700 mb-2 font-bold  text-sm">
                  Security&nbsp;&nbsp;deposit&nbsp;&nbsp;required&nbsp;&nbsp;at&nbsp;&nbsp;check&nbsp;-in
                </label>
                <input
                  type="text"
                  value={formData.securityAmount}
                  className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                  // defaultValue="Urban venue"
                  readOnly
                />
              </div>
            </div>

            <div>
              <label className="block text-gray-700 mb-2 mt-10 font-bold text-sm">
                Advance&nbsp;&nbsp;Payment&nbsp;&nbsp;Mode
              </label>

              <div className="flex">
                <input
                  className="mr-2 mt-[11px] text-sm"
                  defaultChecked
                  type="radio"
                  name="age"
                />
                <p className="text-sm">{formData.advanceMode}</p>
                {formData.advanceMode === "Cash" && (
                  <>
                    <input
                      className="mx-2 mt-[11px]"
                      type="radio"
                      name="age"
                      disabled
                    />
                    <p className="text-sm">Online</p>
                  </>
                )}
                {formData.advanceMode === "Online" && (
                  <>
                    <input
                      className="mx-2 mt-[11px]"
                      type="radio"
                      name="age"
                      disabled
                    />
                    <p className="text-sm">Cash</p>
                  </>
                )}
              </div>
            </div>

            {formData.eventAddOns && formData.eventAddOns.trim() !== "" && (
              <div>
                <label className="block text-gray-700 mb-2 font-bold text-sm">
                  Event Add-Ons
                </label>
                <p className="text-sm border border-gray-300 w-full  pl-3 h-10 rounded overflow-y-auto break-words">
                  {formData.eventAddOns}
                </p>
              </div>
            )}

            {/* Booking Address */}

            <div className="bg-white">
              <label className="block text-gray-700 mb-2 font-bold text-sm">
                Street Address
              </label>
              <input
                type="text"
                value={`${formData.addressLine1}${formData.addressLine2 ? ` ${formData.addressLine2}` : ""
                  }`}
                className="border border-gray-300 w-full  pl-3 h-10 rounded text-sm"
                readOnly
              />
            </div>

            {/* Terms & Conditions */}
            {/* <div className='h-[200px]'>
              <label className="block text-gray-700 mb-2 font-bold">
                Terms & Conditions
              </label>
              <p
                className="border border-gray-300 w-full  pl-3 h-10 rounded  overflow-y-auto break-words">
                {formData.termsConditions || "Terms and Conditions will apply."}
              </p>
              
            </div> */}

            <div className="">
              <label className="block text-gray-700 mb-2 font-bold text-sm">
                Terms & Conditions
              </label>
              <div className="border border-gray-300 w-full p-2 rounded overflow-y-auto break-words">
                {formData.termsConditions
                  ? formData.termsConditions
                    .split(/(?<=\.)\s+/) // Split after each period followed by space
                    .map((sentence, index) => (
                      <p key={index} className="mb-2 text-sm">
                        {sentence.trim()}
                      </p>
                    ))
                  : "Terms and Conditions will apply."}
              </div>
            </div>
          </form>

          <div className="mt-5">
            <label>
              <a
                href={formData.maplink}
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: "blue", cursor: "pointer", fontSize: "14px" }}
              >
                Click to see location on Google Map
              </a>
            </label>
          </div>

          <div className="mt-5">
            <label>
              <a
                href={
                  "https://www.whatsapp.com/channel/0029Vae31H75a23uMtNyFG3Z"
                }
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: "blue", cursor: "pointer", fontSize: "14px" }}
              >
                Follow our WhatsApp channel For Best offers and Last minute
                Deals.
              </a>
            </label>
          </div>

          <div className="pdf-page-break"></div>

          {/* Footer with Contact Info */}
          <div className="w-full flex justify-between items-center mt-[80px]">
            <div className="flex flex-col justify-between items-center">
              <p className="font-semibold text-sm">Scan to visit our Site</p>
              <img
                src={Qr} // Replace this with your logo
                alt="Logo"
                style={{ width: "200px", height: "200px", marginTop: "20px" }}
              />
            </div>
            <div className="flex flex-col justify-end items-end ">
              <div>
                <div className="flex items-center justify-center border border-white p-1 rounded-3xl">
                  <p className="text-gray-700 text-sm">+91-9871371364</p>

                  <div className="bg-red-600 rounded-full w-8 h-8 mt-4 flex items-center justify-center ml-2">
                    <img src={callIcon} alt="Phone" className="w-4 h-4" />
                  </div>

                </div>

                {/* <div className="bg-red-600 rounded-full w-8 h-8 flex items-center justify-center ml-2">
                  <img src={callIcon} alt="Phone" className="w-4 h-4" />
                </div> */}

              </div>
              <div>
                <div className=" flex items-center justify-center border border-white p-1  rounded-3xl ">
                  <p className="text-gray-700 text-sm">info@&nbsp;urbanvenue.in </p>
                  <div className="bg-red-600 rounded-full w-8 h-8 mt-4 flex items-center justify-center ml-2">
                    <img src={emailIcon} alt="Phone" className="w-4 h-4" />
                  </div>
                </div>
              </div>
              <div>
                <div className=" flex items-center justify-center border border-white p-1  rounded-3xl ">
                  <p className="text-gray-700 text-sm">@&nbsp;theurbanvenue</p>
                  <div className="bg-red-600 rounded-full w-8 h-8  mt-4 flex items-center justify-center ml-2">
                    <img src={instagramIcon} alt="Phone" className="w-4 h-4" />
                  </div>
                </div>
              </div>
              <div>
                <div className=" flex items-center justify-center border border-white p-1  rounded-3xl ">
                  <p className="text-gray-700 text-sm">@&nbsp;theurbanvenue</p>
                  <div className="bg-red-600 rounded-full w-8 h-8 mt-4 flex items-center justify-center ml-2">
                    <img src={facebookIcon} alt="Phone" className="w-4 h-4" />
                  </div>
                </div>
              </div>
            </div>
          </div>


          {base64Images.length == 0 && <div className="h-[10px]"></div>}

          <div className="pdf-page-break"></div>

          {base64Images.length > 0 && (
            <label className="block text-gray-700 mt-10 font-bold text-sm">
              Reference Doc
            </label>
          )}
          {base64Images.length > 0 && (
            <div className="my-8 h-[20vh] w-[20vh] gap-4">
              {base64Images.map((image, index) => (
                <img
                  key={index}
                  className=" border border-gray-300 rounded-md"
                  src={image}
                  alt={`Invoice Image ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* {imageExists && (
            <div className="my-8">

              <img
                className="w-full h-[50vh] object-contain"
                src={`${import.meta.env.VITE_BACKEND_URL}${formData.photo}`}
                alt="Passed Image"
              />
            </div>
          )} */}

          {/* Other content */}

          {/* <div className="mt-16 flex justify-center">
            <button
              type="button"
              className="px-6 py-3 text-white bg-red-600 rounded-lg"
              onClick={downloadPDF}
            >
              Download as PDF
            </button>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default InvoiceCopy;

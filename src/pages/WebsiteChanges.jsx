import React from "react";

import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useNavigate } from "react-router-dom";
import { useContext, useEffect, useState } from 'react'
import { ADMIN_DASHBOARD, CREATE_EXECUTIVE, SUPER_ADMIN_DASHBOARD, VenuePage } from "../routes/Routes";
import { EventPage } from "../routes/Routes";
import CustomNavTopbar from "../components/CustomNavTopbar";
import { Toaster } from "react-hot-toast";
import AuthContext from "../context/context";
const WebsiteChanges = () => {
  const navigate = useNavigate();
  const redirect = (data) => {
    switch (data) {
      case "VenuePage":
        return navigate(VenuePage);
      case "EventPage":
        return navigate(EventPage);
    }
  };
  const { usertype } = useContext(AuthContext);

  return (
    <div>
      {/* <Toaster position="top-right" reverseOrder={true} />
      <CustomNavTopbar
        path={usertype === 'Admin' ? ADMIN_DASHBOARD : SUPER_ADMIN_DASHBOARD}
        text={'Create Executive'}
        route={CREATE_EXECUTIVE}
      /> */}
      
      <div className=" w-[100vw] h-[100vh] flex justify-center ">
        <div className="w-[80%] my-16 flex flex-col">
          <div className=" my-4 w-full h-fit grid grid-cols-2 md:grid-cols-1 ">
            <div
              onClick={() => redirect("VenuePage")}
              className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
            >
              <p>Venues</p>
              <ArrowForwardIosIcon />
            </div>

            <div
              onClick={() => redirect("EventPage")}
              className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
            >
              <p>Events</p>
              <ArrowForwardIosIcon />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WebsiteChanges;

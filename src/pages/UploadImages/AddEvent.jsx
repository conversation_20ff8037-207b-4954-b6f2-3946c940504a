import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { EVENY_LOCATION, UPLOAD_EVENT_IMAGES } from "../../routes/Routes";
import axios from "axios";
import { toast } from "react-hot-toast";

const AddEvent = () => {
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState("event");
  const [locations, setLocations] = useState([]);

  // Fetch event locations from API
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await axios.get(
          `${
            import.meta.env.VITE_BACKEND_URL
          }/api/event-locations/get-event-locations`
        );
        setLocations(response.data || []);
      } catch (error) {
        console.error("Error fetching event locations:", error);
        toast.error("Failed to fetch event locations");
      }
    };

    fetchLocations();
  }, []);
  const [selectedLocation, setSelectedLocation] = useState("");
  const [eventName, setEventName] = useState("");

  const handleGoToUploadImages = () => {
    navigate(UPLOAD_EVENT_IMAGES);
  };

  const handleGoToLocation = () => {
    navigate(EVENY_LOCATION);
  };

  const handleAddEvent = async (e) => {
    e.preventDefault();
    if (!eventName || !selectedLocation) {
      toast.error("Please fill all the fields");
      return;
    }

    try {
      // Add event to the database
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/api/events/add-event`,
        {
          category: eventName,
          locationId: selectedLocation,
          locationName: locations.find(
            (place) => place._id === selectedLocation
          )?.locationName,
        }
      );

      if (response.status === 200 || response.status === 201) {
        toast.success(response.data.message || "Event added successfully");

        // Navigate to upload images page with the event data
        navigate(UPLOAD_EVENT_IMAGES, {
          state: {
            event: {
              _id: response.data.eventId || response.data._id,
              locationId: selectedLocation,
              locationName: locations.find(
                (place) => place._id === selectedLocation
              )?.locationName,
              eventName: eventName,
            },
          },
        });

        // Reset form
        setEventName("");
        setSelectedLocation("");
      }
    } catch (error) {
      console.error("Error adding event:", error);
      toast.error(error.response?.data?.message || "Failed to add event");
    }
  };

  return (
    <div className="flex justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white mt-16 rounded-2xl shadow-xl overflow-hidden w-[1057px] h-[659px] transform transition-all hover:shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-300 to-red-400 h-[109px] flex justify-start items-center px-8">
          <div className="flex items-center justify-center bg-white/90 text-red-800 rounded-full w-[200px] h-[60px] font-semibold text-sm text-left px-4 shadow-lg">
            Add Event Category
          </div>
        </div>

        {/* Body */}
        <div className="px-8 py-8">
          {/* Tabs */}
          <div className="flex justify-center mb-8">
            <div className="flex rounded-2xl border-2 border-red-200 overflow-hidden shadow-inner">
              <button
                onClick={handleGoToLocation}
                className={`px-8 py-3 transition-all duration-300 ${
                  selectedTab === "location"
                    ? "bg-gradient-to-r from-red-400 to-red-500 text-white shadow-md"
                    : "bg-white text-red-600 hover:bg-red-50"
                }`}
              >
                Location Name
              </button>
              <button
                onClick={() => setSelectedTab("event")}
                className={`px-8 py-3 transition-all duration-300 ${
                  selectedTab === "event"
                    ? "bg-gradient-to-r from-red-400 to-red-500 text-white shadow-md"
                    : "bg-white text-red-600 hover:bg-red-50"
                }`}
              >
                Add Event
              </button>
            </div>
          </div>

          {/* Form */}
          <form
            className="flex flex-col gap-6 items-start"
            onSubmit={handleAddEvent}
          >
            <div className="w-full">
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Location Name
              </label>
              <select
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
              >
                <option value="">Select a location</option>
                {locations.map((location) => (
                  <option key={location._id} value={location._id}>
                    {location.locationName}
                  </option>
                ))}
              </select>
            </div>

            <div className="w-full">
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Event Name
              </label>
              <input
                type="text"
                value={eventName}
                onChange={(e) => setEventName(e.target.value)}
                placeholder="Enter event name"
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
              />
            </div>

            <div className="mt-4">
              <button
                type="submit"
                className="w-[200px] h-[50px] bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full font-semibold text-sm hover:from-red-500 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Add Event
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddEvent;

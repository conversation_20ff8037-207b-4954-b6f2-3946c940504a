import { useEffect, useState } from "react";
import axios from "axios";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import { UPDATE_VENUE } from "../../routes/Routes";
import { IoMdAdd, IoMdClose } from "react-icons/io";

const UploadVenueImages = () => {
  const [locations, setLocations] = useState([]);
  const [selectedLocationId, setSelectedLocationId] = useState("");
  const [selectedLocationName, setSelectedLocationName] = useState("");
  const [categoryName, setCategoryName] = useState("");
  const [venueLocationName, setVenueLocationName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [price, setPrice] = useState("");
  const [description, setDescription] = useState("");
  const [amenities, setAmenities] = useState("");
  const [selectedImages, setSelectedImages] = useState([]);
  const [venueId, setVenueId] = useState(null);
  const [venues, setVenues] = useState([]);
  const [venueCategories, setVenueCategories] = useState([]);
  const [videos, setVideos] = useState([]);
  const [newVideo, setNewVideo] = useState("");
  const [imageLoading, setImageLoading] = useState({});
  const location = useLocation();
  const navigate = useNavigate();
  const { venue } = location?.state || {};
  console.log("venue---->", venue);

  useEffect(() => {
    if (venue) {
      console.log("Received venue data:", venue);
      setVenueId(venue._id);
      setSelectedLocationId(venue.locationId);
      setSelectedLocationName(venue.locationName);
      setCategoryName(venue.categoryName);
      setVenueLocationName(venue.venueLocationName);
      setPhoneNumber(venue.phoneNumber);
      setPrice(venue.price);
      setDescription(venue.description || "");
      setAmenities(
        Array.isArray(venue.amenities)
          ? venue.amenities.join(",")
          : venue.amenities || ""
      );
      const images = venue.images || [];
      console.log("Venue images:", images);
      setSelectedImages(images);

      // Handle videos - convert from string or array to array
      const venueVideos = venue.videos || [];
      if (Array.isArray(venueVideos)) {
        setVideos(venueVideos);
      } else if (typeof venueVideos === "string" && venueVideos.trim() !== "") {
        setVideos([venueVideos]);
      } else {
        setVideos([]);
      }

      // Initialize loading state for existing images
      const initialLoadingState = {};
      images.forEach((_, idx) => {
        initialLoadingState[idx] = true;
      });
      setImageLoading(initialLoadingState);
    }
  }, [venue]);

  console.log("venueId---->", venueId);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_BACKEND_URL}/api/venues/get-venues`
        );
        const venuesData = response.data.venues || [];
        setVenues(venuesData);

        const locationsdata = venuesData.map((venue) => ({
          locationId: venue.locationId,
          locationName: venue.locationName,
        }));

        const uniqueLocations = Array.from(
          new Map(locationsdata.map((item) => [item.locationId, item])).values()
        );
        setLocations(uniqueLocations);
      } catch (error) {
        console.error("Error fetching venues:", error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedLocationId && venues.length > 0) {
      const filteredVenues = venues.filter(
        (venue) => venue.locationId === selectedLocationId
      );
      const uniqueCategories = [
        ...new Set(filteredVenues.map((venue) => venue.category)),
      ];
      setVenueCategories(uniqueCategories);
    } else {
      setVenueCategories([]);
    }
  }, [selectedLocationId, venues]);

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);

    // Check file size - 20MB limit per file
    const oversizedFiles = files.filter((file) => file.size > 20 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      toast.error("One or more files exceed the 20MB size limit");
      return;
    }

    const newImages = [...selectedImages, ...files];
    setSelectedImages(newImages);

    // Set loading state for new images
    const newLoadingState = { ...imageLoading };
    files.forEach((_, idx) => {
      const newIndex = selectedImages.length + idx;
      newLoadingState[newIndex] = true;
    });
    setImageLoading(newLoadingState);
  };

  const handleLocationChange = (e) => {
    const selectedLocation = locations.find(
      (loc) => loc.locationId === e.target.value
    );

    if (selectedLocation) {
      setSelectedLocationId(selectedLocation.locationId);
      setSelectedLocationName(selectedLocation.locationName);
    } else {
      setSelectedLocationId("");
      setSelectedLocationName("");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!selectedLocationId || !selectedLocationName) {
      toast.error("Please select a location");
      return;
    }

    const formData = new FormData();
    formData.append("locationId", selectedLocationId);
    formData.append("locationName", selectedLocationName);
    formData.append("categoryName", categoryName);
    formData.append("venueLocationName", venueLocationName);
    formData.append("phoneNumber", phoneNumber);
    formData.append("price", price);
    formData.append("description", description);
    formData.append("amenities", amenities);

    // Append each video URL separately
    if (videos.length > 0) {
      videos.forEach((videoUrl) => {
        formData.append("videos", videoUrl);
      });
    }

    // If we're updating, add the venueDetailsId
    if (venueId) {
      formData.append("venueDetailsId", venueId);
      formData.append("replaceImages", "false");
    }

    // Handle image uploads
    selectedImages.forEach((image) => {
      if (typeof image !== "string") {
        formData.append("images", image);
      }
    });

    try {
      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };

      let response;
      if (venueId) {
        // Update existing venue using PUT
        response = await axios.put(
          `${
            import.meta.env.VITE_BACKEND_URL
          }/api/allVenues/update-venue-details`,
          formData,
          config
        );
      } else {
        // Create new venue using POST
        response = await axios.post(
          `${import.meta.env.VITE_BACKEND_URL}/api/allVenues/add-venue-details`,
          formData,
          config
        );
      }

      if (response.data.success) {
        toast.success(
          venueId ? "Venue updated successfully!" : "Venue added successfully!"
        );

        if (!venueId) {
          // Reset form fields for new venue
          setSelectedLocationId("");
          setSelectedLocationName("");
          setCategoryName("");
          setVenueLocationName("");
          setPhoneNumber("");
          setPrice("");
          setDescription("");
          setAmenities("");
          setSelectedImages([]);
          setVideos([]);
          setNewVideo("");
        } else {
          // Navigate back to venues list after successful update
          toast.success("Redirecting to venues list...");
          navigate(UPDATE_VENUE);
        }
      } else {
        throw new Error(response.data.message || "Failed to process venue");
      }
    } catch (error) {
      console.error("Error submitting venue:", error);
      toast.error(
        `Failed to ${venueId ? "update" : "add"} venue: ${
          error.response?.data?.message || error.message
        }`
      );
    }
  };

  // Function to remove an image from the selected images
  const handleRemoveImage = (index) => {
    const newImages = [...selectedImages];
    newImages.splice(index, 1);
    setSelectedImages(newImages);

    // Update loading state
    const newLoadingState = { ...imageLoading };
    delete newLoadingState[index];

    // Reindex the loading states for images after the removed one
    Object.keys(newLoadingState).forEach((key) => {
      const keyNum = parseInt(key);
      if (keyNum > index) {
        newLoadingState[keyNum - 1] = newLoadingState[keyNum];
        delete newLoadingState[keyNum];
      }
    });

    setImageLoading(newLoadingState);
  };

  return (
    <div className="flex justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white rounded-2xl shadow-xl w-[1057px] mt-7 transform transition-all hover:shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-300 to-red-400 h-[109px] flex justify-start items-center px-8 rounded-t-2xl">
          <div className="flex items-center justify-center bg-white/90 text-red-800 rounded-full w-[200px] h-[60px] font-semibold text-sm text-left px-4 shadow-lg">
            {venueId ? "Edit Venue" : "Add Venue"}
          </div>
        </div>

        {/* Form Body */}
        <div className="p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Select Location */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Select Location
              </label>
              <select
                value={selectedLocationId}
                onChange={handleLocationChange}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
              >
                <option value="">Select a Location</option>
                {locations.map((location) => (
                  <option key={location.locationId} value={location.locationId}>
                    {location.locationName}
                  </option>
                ))}
              </select>
            </div>

            {/* Select Category */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Select Category
              </label>
              <select
                disabled={!selectedLocationId}
                value={categoryName}
                onChange={(e) => setCategoryName(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
              >
                <option value="">Select a Category</option>
                {venueCategories.map((category, index) => (
                  <option key={index} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Venue Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  FarmHouse Name
                </label>
                <input
                  type="text"
                  placeholder="Enter farm house name"
                  value={venueLocationName}
                  onChange={(e) => setVenueLocationName(e.target.value)}
                  className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  placeholder="Enter phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  Price
                </label>
                <input
                  type="number"
                  placeholder="Enter price"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  YouTube Video URLs
                </label>
                <div className="space-y-2">
                  {/* Display existing videos with remove button */}
                  {videos.map((videoUrl, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={videoUrl}
                        onChange={(e) => {
                          const updatedVideos = [...videos];
                          updatedVideos[index] = e.target.value;
                          setVideos(updatedVideos);
                        }}
                        className="flex-1 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const updatedVideos = [...videos];
                          updatedVideos.splice(index, 1);
                          setVideos(updatedVideos);
                        }}
                        className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
                      >
                        <IoMdClose className="h-5 w-5" />
                      </button>
                    </div>
                  ))}

                  {/* Add new video input */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      placeholder="Enter YouTube video URL"
                      value={newVideo}
                      onChange={(e) => setNewVideo(e.target.value)}
                      className="flex-1 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        if (newVideo.trim()) {
                          setVideos([...videos, newVideo.trim()]);
                          setNewVideo("");
                        }
                      }}
                      className="bg-green-500 text-white p-2 rounded-full hover:bg-green-600 transition-colors"
                    >
                      <IoMdAdd className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Description
              </label>
              <textarea
                placeholder="Enter description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200 min-h-[120px]"
                required
              />
            </div>

            {/* Amenities */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Amenities
              </label>
              <input
                type="text"
                placeholder="Enter amenities (comma-separated)"
                value={amenities}
                onChange={(e) => setAmenities(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                required
              />
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Upload Images (Max 20MB per image)
              </label>
              <div className="border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-red-300 transition-all duration-200">
                <input
                  type="file"
                  name="images"
                  accept="image/*"
                  multiple
                  onChange={handleImageChange}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer block">
                  <div className="flex flex-col items-center">
                    <svg
                      className="w-12 h-12 text-gray-400 mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <span className="text-gray-600">
                      Click to upload images or drag and drop
                    </span>
                    <span className="text-sm text-gray-500 mt-1">
                      Selected Images: {selectedImages.length}
                    </span>
                  </div>
                </label>
              </div>

              {/* Display selected images */}
              {selectedImages.length > 0 && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
                  {selectedImages.map((image, index) => (
                    <div key={index} className="relative group">
                      <div className="h-24 w-full rounded-lg overflow-hidden border border-gray-200">
                        {typeof image === "string" ? (
                          <img
                            src={
                              image.startsWith("http")
                                ? image
                                : image.startsWith("/")
                                ? `${import.meta.env.VITE_BACKEND_URL}${image}`
                                : `${import.meta.env.VITE_BACKEND_URL}/${image}`
                            }
                            alt={`Selected ${index + 1}`}
                            className="h-full w-full object-cover"
                            onLoad={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }))
                            }
                            onError={(e) => {
                              console.error("Image load error:", e);
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }));
                            }}
                          />
                        ) : (
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Selected ${index + 1}`}
                            className="h-full w-full object-cover"
                            onLoad={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }))
                            }
                            onError={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }))
                            }
                          />
                        )}
                        {imageLoading[index] && (
                          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50">
                            <svg
                              className="animate-spin h-6 w-6 text-red-500"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                          </div>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                        title="Remove image"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                className="w-[200px] h-[50px] bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full font-semibold text-sm hover:from-red-500 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                {venueId ? "Update Venue" : "Add Venue"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UploadVenueImages;

import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import axios from "axios";
import { UPDATE_EVENT } from "../../routes/Routes";
import { IoMdAdd, IoMdClose } from "react-icons/io";

const UploadEventImages = () => {
  const [locations, setLocations] = useState([]);
  const [selectedLocationId, setSelectedLocationId] = useState("");
  const [selectedLocationName, setSelectedLocationName] = useState("");
  const [categoryName, setCategoryName] = useState("");
  const [venueLocationName, setVenueLocationName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [price, setPrice] = useState("");
  const [description, setDescription] = useState("");
  const [amenities, setAmenities] = useState("");
  const [selectedImages, setSelectedImages] = useState([]);
  const [eventId, setEventId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [events, setEvents] = useState([]);
  const [venueCategories, setVenueCategories] = useState([]);
  const [videos, setVideos] = useState([]);
  const [newVideo, setNewVideo] = useState("");
  const [imageLoading, setImageLoading] = useState({});

  const location = useLocation();
  const navigate = useNavigate();
  const { event } = location?.state || {};

  // Fetch event locations from API
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const response = await axios.get(
          `http://localhost:9000/api/events/get-events`
        );
        if (response.data && response.data.events) {
          // Extract unique locations from events
          const eventsData = response.data.events || [];
          const locationsData = eventsData.map((event) => ({
            locationId: event.locationId,
            locationName: event.locationName,
          }));

          const uniqueLocations = Array.from(
            new Map(
              locationsData.map((item) => [item.locationId, item])
            ).values()
          );
          setLocations(uniqueLocations);
          setEvents(eventsData);
        } else {
          setLocations([]);
          setEvents([]);
        }
      } catch (error) {
        console.error("Error fetching events:", error);
        toast.error("Failed to fetch events");
      }
    };

    // Check available API endpoints
    const checkApiEndpoints = async () => {
      try {
        // Try to make a simple OPTIONS request to check available endpoints
        const response = await axios.options(
          `http://localhost:9000/api/events`
        );
        console.log("Available API endpoints:", response.data);
      } catch (error) {
        console.log("API endpoints check failed:", error);
        // This might fail but it's just for debugging
      }
    };

    fetchLocations();
    checkApiEndpoints();
  }, []);

  // Update event categories based on selected location
  useEffect(() => {
    if (selectedLocationId && events.length > 0) {
      const filteredEvents = events.filter(
        (event) => event.locationId === selectedLocationId
      );
      const uniqueCategories = [
        ...new Set(filteredEvents.map((event) => event.category)),
      ];
      setVenueCategories(uniqueCategories);
    } else {
      setVenueCategories([]);
    }
  }, [selectedLocationId, events]);

  // Set form data if event is passed from navigation
  useEffect(() => {
    if (event) {
      console.log("Received event data:", event);
      setEventId(event._id);
      setSelectedLocationId(event.locationId);
      setSelectedLocationName(event.locationName);
      setCategoryName(event.categoryName || "");
      setVenueLocationName(event.venueLocationName || "");
      setPhoneNumber(event.phoneNumber || "");
      setPrice(event.price || "");
      setDescription(event.description || "");
      setAmenities(event.amenities || "");
      const images = event.images || [];
      console.log("Event images:", images);
      setSelectedImages(images);

      // Handle videos - convert from string or array to array
      const eventVideos = event.videos || [];
      if (Array.isArray(eventVideos)) {
        setVideos(eventVideos);
      } else if (typeof eventVideos === "string" && eventVideos.trim() !== "") {
        setVideos([eventVideos]);
      } else {
        setVideos([]);
      }

      // Initialize loading state for existing images
      const initialLoadingState = {};
      images.forEach((_, idx) => {
        initialLoadingState[idx] = true;
      });
      setImageLoading(initialLoadingState);
    }
  }, [event]);

  console.log("eventId---->", eventId);

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);

    // Check file size - 20MB limit per file
    const oversizedFiles = files.filter((file) => file.size > 20 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      toast.error("One or more files exceed the 20MB size limit");
      return;
    }

    const newImages = [...selectedImages, ...files];
    setSelectedImages(newImages);

    // Set loading state for new images
    const newLoadingState = { ...imageLoading };
    files.forEach((_, idx) => {
      const newIndex = selectedImages.length + idx;
      newLoadingState[newIndex] = true;
    });
    setImageLoading(newLoadingState);
  };

  const handleLocationChange = (e) => {
    const selectedLocation = locations.find(
      (loc) => loc.locationId === e.target.value
    );

    if (selectedLocation) {
      setSelectedLocationId(selectedLocation.locationId);
      setSelectedLocationName(selectedLocation.locationName);
    } else {
      setSelectedLocationId("");
      setSelectedLocationName("");
    }

    // Reset category when location changes
    setCategoryName("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!selectedLocationId || !selectedLocationName) {
      toast.error("Please select a location");
      return;
    }

    if (!categoryName) {
      toast.error("Please select a category");
      return;
    }

    if (!venueLocationName) {
      toast.error("Please enter event location name");
      return;
    }

    setIsLoading(true);

    try {
      // Create FormData object for file upload - match backend field names exactly
      const formData = new FormData();
      formData.append("locationId", selectedLocationId);
      formData.append("locationName", selectedLocationName);
      formData.append("categoryName", categoryName);
      formData.append("eventLocationName", venueLocationName); // Changed from venueLocationName to eventLocationName
      formData.append("phoneNumber", phoneNumber);
      formData.append("price", price);
      formData.append("description", description);
      formData.append("amenities", amenities);

      // Append each video URL separately
      if (videos.length > 0) {
        videos.forEach((videoUrl) => {
          formData.append("videos", videoUrl);
        });
      }

      // If we're updating, add the eventDetailsId
      if (eventId) {
        formData.append("eventDetailsId", eventId);
        formData.append("replaceImages", "false");
      }

      // Handle image uploads - only append new images (not string URLs)
      for (const image of selectedImages) {
        if (typeof image !== "string") {
          formData.append("images", image);
        }
      }

      // Log all form data for debugging
      console.log("Submitting form data with locationId:", selectedLocationId);
      console.log(
        "Submitting form data with locationName:",
        selectedLocationName
      );
      console.log("Submitting form data with categoryName:", categoryName);
      console.log(
        "Submitting form data with eventLocationName:",
        venueLocationName
      );
      console.log(
        "Submitting form data with images count:",
        selectedImages.filter((img) => typeof img !== "string").length
      );

      // Log all form data entries
      console.log("Form data entries:");
      for (const pair of formData.entries()) {
        console.log(pair[0], pair[1]);
      }

      try {
        const config = {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        };

        let response;
        if (eventId) {
          // Update existing event using PUT
          response = await axios.put(
            `http://localhost:9000/api/allEvents/update-event-details`,
            formData,
            config
          );
        } else {
          // Create new event using POST - use the same endpoint pattern as venues
          response = await axios.post(
            `http://localhost:9000/api/allEvents/add-event-details`,
            formData,
            config
          );
        }

        console.log("API Response:", response.data);

        if (response.data.success) {
          toast.success(
            eventId
              ? "Event updated successfully!"
              : "Event added successfully!"
          );

          if (!eventId) {
            // Reset form fields for new event
            setSelectedLocationId("");
            setSelectedLocationName("");
            setCategoryName("");
            setVenueLocationName("");
            setPhoneNumber("");
            setPrice("");
            setDescription("");
            setAmenities("");
            setSelectedImages([]);
            setVideos([]);
            setNewVideo("");
          } else {
            // Navigate back to events list after successful update
            toast.success("Redirecting to events list...");
            navigate(UPDATE_EVENT);
          }
        } else {
          throw new Error(response.data.message || "Failed to process event");
        }
      } catch (error) {
        console.error("Error submitting event:", error);

        // Try alternative endpoint if the first one fails
        try {
          console.log("Trying alternative endpoint...");

          // Try with a slightly different endpoint name (singular vs plural)
          const alternativeFormData = new FormData();
          alternativeFormData.append("locationId", selectedLocationId);
          alternativeFormData.append("locationName", selectedLocationName);
          alternativeFormData.append("categoryName", categoryName);
          alternativeFormData.append("eventLocationName", venueLocationName);
          alternativeFormData.append("phoneNumber", phoneNumber);
          alternativeFormData.append("price", price);
          alternativeFormData.append("description", description);
          alternativeFormData.append("amenities", amenities);

          // Append each video URL separately
          if (videos.length > 0) {
            videos.forEach((videoUrl) => {
              alternativeFormData.append("videos", videoUrl);
            });
          }

          // Handle image uploads
          for (const image of selectedImages) {
            if (typeof image !== "string") {
              alternativeFormData.append("images", image);
            }
          }

          const alternativeResponse = await axios.post(
            `http://localhost:9000/api/allEvents/add-events-details`,
            alternativeFormData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            }
          );

          console.log("Alternative response:", alternativeResponse.data);

          if (alternativeResponse.data.success) {
            toast.success("Event added successfully!");

            // Reset form fields
            setSelectedLocationId("");
            setSelectedLocationName("");
            setCategoryName("");
            setVenueLocationName("");
            setPhoneNumber("");
            setPrice("");
            setDescription("");
            setAmenities("");
            setSelectedImages([]);
            setVideos([]);
            setNewVideo("");
          } else {
            throw new Error(
              alternativeResponse.data.message || "Failed to process event"
            );
          }
        } catch (alternativeError) {
          console.error("Alternative endpoint failed:", alternativeError);

          // Try a third approach with a different content type
          try {
            console.log("Trying third approach with application/json...");

            // Create a plain JSON object without files
            const jsonData = {
              locationId: selectedLocationId,
              locationName: selectedLocationName,
              categoryName: categoryName,
              eventLocationName: venueLocationName,
              phoneNumber: phoneNumber,
              price: Number(price),
              description: description,
              amenities: amenities,
              videos: videos,
            };

            // Try to create the basic event structure first
            const jsonResponse = await axios.post(
              `http://localhost:9000/api/allEvents/add-event-details`,
              jsonData,
              {
                headers: {
                  "Content-Type": "application/json",
                },
              }
            );

            console.log("JSON approach response:", jsonResponse.data);

            if (jsonResponse.data.success) {
              toast.success("Event added successfully (without images)!");
              toast.info("You may need to upload images separately.");

              // Reset form fields
              setSelectedLocationId("");
              setSelectedLocationName("");
              setCategoryName("");
              setVenueLocationName("");
              setPhoneNumber("");
              setPrice("");
              setDescription("");
              setAmenities("");
              setSelectedImages([]);
              setVideos([]);
              setNewVideo("");
            } else {
              throw new Error(
                jsonResponse.data.message || "Failed to process event"
              );
            }
          } catch (thirdError) {
            console.error("Third approach failed:", thirdError);
            toast.error(
              `Failed to ${eventId ? "update" : "add"} event: ${
                error.response?.data?.message || error.message
              }`
            );
          }
        }
      }
    } catch (error) {
      console.error("Error saving event:", error);
      toast.error(error.response?.data?.message || "Failed to save event");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to remove an image from the selected images
  const handleRemoveImage = (index) => {
    const newImages = [...selectedImages];
    newImages.splice(index, 1);
    setSelectedImages(newImages);

    // Update loading state
    const newLoadingState = { ...imageLoading };
    delete newLoadingState[index];

    // Reindex the loading states for images after the removed one
    Object.keys(newLoadingState).forEach((key) => {
      const keyNum = parseInt(key);
      if (keyNum > index) {
        newLoadingState[keyNum - 1] = newLoadingState[keyNum];
        delete newLoadingState[keyNum];
      }
    });

    setImageLoading(newLoadingState);
  };

  return (
    <div className="flex justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white rounded-2xl shadow-xl w-[1057px] mt-7 transform transition-all hover:shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-300 to-red-400 h-[109px] flex justify-start items-center px-8 rounded-t-2xl">
          <div className="flex items-center justify-center bg-white/90 text-red-800 rounded-full w-[200px] h-[60px] font-semibold text-sm text-left px-4 shadow-lg">
            {eventId ? "Edit Event Details" : "Add Event Details"}
          </div>
        </div>

        {/* Form Body */}
        <div className="p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Select Location */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Select Location
              </label>
              <select
                value={selectedLocationId}
                onChange={handleLocationChange}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
              >
                <option value="">Select a Location</option>
                {locations.map((location) => (
                  <option key={location.locationId} value={location.locationId}>
                    {location.locationName}
                  </option>
                ))}
              </select>
            </div>

            {/* Select Category */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Select Category
              </label>
              <select
                disabled={!selectedLocationId}
                value={categoryName}
                onChange={(e) => setCategoryName(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
              >
                <option value="">Select a Category</option>
                {venueCategories.map((category, index) => (
                  <option key={index} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Venue Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  Event Location Name
                </label>
                <input
                  type="text"
                  placeholder="Enter event location name"
                  value={venueLocationName}
                  onChange={(e) => setVenueLocationName(e.target.value)}
                  className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  placeholder="Enter phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  Price
                </label>
                <input
                  type="number"
                  placeholder="Enter price"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-800 font-semibold text-sm mb-2">
                  YouTube Video URLs
                </label>
                <div className="space-y-2">
                  {/* Display existing videos with remove button */}
                  {videos.map((videoUrl, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={videoUrl}
                        onChange={(e) => {
                          const updatedVideos = [...videos];
                          updatedVideos[index] = e.target.value;
                          setVideos(updatedVideos);
                        }}
                        className="flex-1 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const updatedVideos = [...videos];
                          updatedVideos.splice(index, 1);
                          setVideos(updatedVideos);
                        }}
                        className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
                      >
                        <IoMdClose className="h-5 w-5" />
                      </button>
                    </div>
                  ))}

                  {/* Add new video input */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      placeholder="Enter YouTube video URL"
                      value={newVideo}
                      onChange={(e) => setNewVideo(e.target.value)}
                      className="flex-1 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        if (newVideo.trim()) {
                          setVideos([...videos, newVideo.trim()]);
                          setNewVideo("");
                        }
                      }}
                      className="bg-green-500 text-white p-2 rounded-full hover:bg-green-600 transition-colors"
                    >
                      <IoMdAdd className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Description
              </label>
              <textarea
                placeholder="Enter description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200 min-h-[120px]"
                required
              />
            </div>

            {/* Amenities */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Amenities
              </label>
              <input
                type="text"
                placeholder="Enter amenities (comma-separated)"
                value={amenities}
                onChange={(e) => setAmenities(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                required
              />
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Upload Images (Max 20MB per image)
              </label>
              <div className="border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-red-300 transition-all duration-200">
                <input
                  type="file"
                  name="images"
                  accept="image/*"
                  multiple
                  onChange={handleImageChange}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer block">
                  <div className="flex flex-col items-center">
                    <svg
                      className="w-12 h-12 text-gray-400 mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <span className="text-gray-600">
                      Click to upload images or drag and drop
                    </span>
                    <span className="text-sm text-gray-500 mt-1">
                      Selected Images: {selectedImages.length}
                    </span>
                  </div>
                </label>
              </div>

              {/* Display selected images */}
              {selectedImages.length > 0 && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
                  {selectedImages.map((image, index) => (
                    <div key={index} className="relative group">
                      <div className="h-24 w-full rounded-lg overflow-hidden border border-gray-200">
                        {typeof image === "string" ? (
                          <img
                            src={
                              image.startsWith("http")
                                ? image
                                : image.startsWith("/")
                                ? `${import.meta.env.VITE_BACKEND_URL}${image}`
                                : `${import.meta.env.VITE_BACKEND_URL}/${image}`
                            }
                            alt={`Selected ${index + 1}`}
                            className="h-full w-full object-cover"
                            onLoad={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }))
                            }
                            onError={(e) => {
                              console.error("Image load error:", e);
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }));
                            }}
                          />
                        ) : (
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Selected ${index + 1}`}
                            className="h-full w-full object-cover"
                            onLoad={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }))
                            }
                            onError={() =>
                              setImageLoading((prev) => ({
                                ...prev,
                                [index]: false,
                              }))
                            }
                          />
                        )}
                        {imageLoading[index] && (
                          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50">
                            <svg
                              className="animate-spin h-6 w-6 text-red-500"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                          </div>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                        title="Remove image"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                className="w-[200px] h-[50px] bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full font-semibold text-sm hover:from-red-500 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                {eventId ? "Update Event Details" : "Add Event Details"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UploadEventImages;

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ADD_EVENT } from "../../routes/Routes";
import axios from "axios";
import { toast } from "react-hot-toast";

const EvenyLocation = () => {
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState("location");
  const [locationName, setLocationName] = useState("");

  const handleAddVenueClick = () => {
    setSelectedTab("venue");
    navigate(ADD_EVENT);
  };

  const handleAddLocation = async () => {
    if (!locationName.trim()) {
      toast.error("Location name is required");
      return;
    }
    
    try {
      const response = await axios.post(
        `${
          import.meta.env.VITE_BACKEND_URL
        }/api/event-locations/add-event-location`,
        { locationName }
      );

      if (response.status === 200 || response.status === 201) {
        toast.success(
          response.data.message || "Event location added successfully"
        );
        navigate(ADD_EVENT);
        setLocationName("");
      }
    } catch (error) {
      console.error("Error adding event location:", error);
      toast.error(error.response?.data?.message || "Something went wrong");
    }
  };

  return (
    <div className="flex justify-center  min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white rounded-2xl mt-16 shadow-xl overflow-hidden w-[1057px] h-[659px] transform transition-all hover:shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-300 to-red-400 h-[109px] flex justify-start items-center px-8">
          <div className="flex items-center justify-center bg-white/90 text-red-800 rounded-full w-[200px] h-[60px] font-semibold text-sm text-left px-4 shadow-lg">
            Add Location
          </div>
        </div>

        {/* Body */}
        <div className="px-8 py-8">
          {/* Tabs */}
          <div className="flex justify-center mb-8">
            <div className="flex rounded-2xl border-2 border-red-200 overflow-hidden shadow-inner">
              <button
                onClick={() => setSelectedTab("location")}
                className={`px-8 py-3 transition-all duration-300 ${
                  selectedTab === "location"
                    ? "bg-gradient-to-r from-red-400 to-red-500 text-white shadow-md"
                    : "bg-white text-red-600 hover:bg-red-50"
                }`}
              >
                Location Name
              </button>
              <button
                onClick={handleAddVenueClick}
                className={`px-8 py-3 transition-all duration-300 ${
                  selectedTab === "venue"
                    ? "bg-gradient-to-r from-red-400 to-red-500 text-white shadow-md"
                    : "bg-white text-red-600 hover:bg-red-50"
                }`}
              >
                Add Event
              </button>
            </div>
          </div>

          {/* Form */}
          <form
            className="flex flex-col gap-6 items-start"
            onSubmit={(e) => e.preventDefault()}
          >
            <div className="w-full">
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Location Name
              </label>
              <input
                type="text"
                placeholder="Enter location name"
                value={locationName}
                onChange={(e) => setLocationName(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
              />
            </div>

            <div className="mt-4">
              <button
                type="button"
                onClick={handleAddLocation}
                className="w-[200px] h-[50px] bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full font-semibold text-sm hover:from-red-500 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Add Location
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EvenyLocation;

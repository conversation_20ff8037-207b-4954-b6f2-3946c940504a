import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { toast } from "react-hot-toast";
import axios from "axios";
import { UPLOAD_EVENT_IMAGES } from "../../routes/Routes";

const UpdateEvent = () => {
  const navigate = useNavigate();
  const [selectedLocation, setSelectedLocation] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [eventToDelete, setEventToDelete] = useState(null);
  const [events, setEvents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch events from API
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const response = await axios.get(
          `${
            import.meta.env.VITE_BACKEND_URL
          }/api/allEvents/get-all-event-details`
        );

        if (response.data && response.data.success) {
          console.log("response.data.data---->", response.data.data);
          setEvents(response.data.data || []);
        } else {
          setEvents([]);
        }
        setError(null);
      } catch (error) {
        console.error("Error fetching events:", error);
        setError("Failed to fetch events. Please try again later.");
        toast.error("Failed to fetch events");
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Get unique locations
  const locations = events.map((event) => ({
    id: event.locationId,
    name: event.locationName,
  }));

  // Update categories when location changes
  useEffect(() => {
    if (selectedLocation) {
      const selectedEvent = events.find(
        (event) => event.locationId === selectedLocation
      );
      if (selectedEvent) {
        const eventCategories = selectedEvent.eventDetails.map(
          (detail) => detail.categoryName
        );
        setCategories(eventCategories);
      }
    } else {
      setCategories([]);
    }
    setSelectedCategory("");
  }, [selectedLocation, events]);

  // Filter events based on selected location and category
  const filteredEvents = events
    .filter(
      (event) => !selectedLocation || event.locationId === selectedLocation
    )
    .flatMap((event) =>
      event.eventDetails
        .filter(
          (detail) =>
            !selectedCategory || detail.categoryName === selectedCategory
        )
        .flatMap((detail) =>
          detail.eventDetails.map((eventDetail) => ({
            ...eventDetail,
            locationName: event.locationName,
            categoryName: detail.categoryName,
            locationId: event.locationId,
            eventId: eventDetail._id,
            parentEvent: event,
            eventLocationName: eventDetail.locationName,
          }))
        )
    );

  const handleEdit = (event) => {
    const editData = {
      _id: event.eventId,
      locationId: event.locationId,
      locationName: event.locationName,
      categoryName: event.categoryName,
      venueLocationName: event.eventLocationName, // Using venueLocationName for consistency with form
      phoneNumber: event.phoneNumber,
      price: event.price,
      description: event.description,
      amenities: Array.isArray(event.amenities)
        ? event.amenities
        : typeof event.amenities === "string"
        ? event.amenities.split(",")
        : event.amenities || [],
      images: event.images,
      videos: event.videos || (event.video ? [event.video] : []),
    };

    navigate(UPLOAD_EVENT_IMAGES, { state: { event: editData } });
  };

  const handleDelete = (event) => {
    setEventToDelete(event);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      const deleteData = {
        locationId: eventToDelete.locationId,
        categoryName: eventToDelete.categoryName,
        eventDetailsId: eventToDelete.eventId,
      };

      const response = await axios.delete(
        `${
          import.meta.env.VITE_BACKEND_URL
        }/api/allEvents/delete-event-details`,
        { data: deleteData }
      );

      if (response.data.success) {
        toast.success("Event deleted successfully!");
        // Refresh the events list
        const updatedResponse = await axios.get(
          `${
            import.meta.env.VITE_BACKEND_URL
          }/api/allEvents/get-all-event-details`
        );
        if (updatedResponse.data.success) {
          setEvents(updatedResponse.data.data);
        }
      } else {
        throw new Error(response.data.message || "Failed to delete event");
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      toast.error(
        `Failed to delete event: ${
          error.response?.data?.message || error.message
        }`
      );
    } finally {
      setShowDeleteDialog(false);
      setEventToDelete(null);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white rounded-2xl shadow-xl w-[1057px] mt-7 transform transition-all hover:shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-300 to-red-400 h-[109px] flex justify-start items-center px-8 rounded-t-2xl">
          <div className="flex items-center justify-center bg-white/90 text-red-800 rounded-full w-[200px] h-[60px] font-semibold text-sm text-left px-4 shadow-lg">
            All Events
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <p className="text-gray-500">Loading events...</p>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-64">
              <p className="text-red-500">{error}</p>
            </div>
          ) : (
            <>
              {/* Select Boxes */}
              <div className="flex justify-between gap-6 mb-8">
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-1/2 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                >
                  <option value="">All Locations</option>
                  {locations.map((location) => (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-1/2 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
                >
                  <option value="">All Categories</option>
                  {categories.map((category, index) => (
                    <option key={index} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Event Cards */}
              <div className="flex flex-wrap justify-center gap-8">
                {filteredEvents.length === 0 ? (
                  <div className="flex justify-center items-center h-64 w-full">
                    <p className="text-gray-500">No events found</p>
                  </div>
                ) : (
                  filteredEvents.map((event, index) => (
                    <div
                      key={index}
                      className="w-[400px] h-[400px] border-2 border-gray-100 rounded-2xl p-4 shadow-lg bg-white hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col"
                    >
                      {/* Image Container */}
                      <div className="relative w-full h-[300px] rounded-xl overflow-hidden group">
                        {event.images && event.images.length > 0 ? (
                          <>
                            <img
                              src={`${import.meta.env.VITE_BACKEND_URL}${
                                event.images[0]
                              }`}
                              alt={event.eventLocationName}
                              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                              loading="lazy"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </>
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <div className="text-center">
                              <span className="text-gray-400 text-sm block">
                                No Image Available
                              </span>
                              <span className="text-gray-300 text-xs block mt-1">
                                Upload event images
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 flex flex-col justify-between mt-4">
                        <div>
                          <div className="flex flex-row justify-between">
                            <h3 className="text-xl font-semibold text-gray-800 truncate">
                              {event.eventLocationName}
                            </h3>

                            <span className="px-3 py-1 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full text-sm font-medium shadow-md backdrop-blur-sm bg-opacity-90">
                              ₹{event.price}
                            </span>
                          </div>

                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-base text-gray-500">
                              Category:
                            </span>
                            <span className="text-base font-medium text-gray-700">
                              {event.categoryName}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-base text-gray-500">
                              Location:
                            </span>
                            <span className="text-base font-medium text-gray-700 truncate">
                              {event.locationName}
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 mt-4">
                          <button
                            onClick={() => handleEdit(event)}
                            className="p-3 bg-gray-50 rounded-full hover:bg-gray-100 transition-all duration-200 shadow-sm hover:shadow-md"
                            title="Edit Event"
                          >
                            <EditIcon
                              className="text-gray-600"
                              style={{ fontSize: "1.5rem" }}
                            />
                          </button>
                          <button
                            onClick={() => handleDelete(event)}
                            className="p-3 bg-red-50 rounded-full hover:bg-red-100 transition-all duration-200 shadow-sm hover:shadow-md"
                            title="Delete Event"
                          >
                            <DeleteIcon
                              className="text-red-600"
                              style={{ fontSize: "1.5rem" }}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl transform transition-all duration-300">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              Delete Event
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this event? This action cannot be
              undone.
            </p>
            <div className="flex justify-end gap-4">
              <button
                onClick={() => {
                  setShowDeleteDialog(false);
                  setEventToDelete(null);
                }}
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-6 py-2 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full hover:from-red-500 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpdateEvent;

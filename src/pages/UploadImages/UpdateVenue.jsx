import React, { useEffect, useState } from "react";
import axios from "axios";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import { Toaster } from "react-hot-toast";
import { UPLOAD_VENUE_IMAGES } from "../../routes/Routes";

const UpdateVenue = () => {
  const navigate = useNavigate();
  const [venues, setVenues] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [categories, setCategories] = useState([]);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [venueToDelete, setVenueToDelete] = useState(null);

  useEffect(() => {
    const fetchVenues = async () => {
      try {
        const response = await axios.get(
          `${
            import.meta.env.VITE_BACKEND_URL
          }/api/allVenues/get-all-venue-details`
        );

        if (response.data.success) {
          console.log("response.data.data---->", response.data.data);
          setVenues(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching venues:", error);
      }
    };
    fetchVenues();
  }, []);

  // Get unique locations
  const locations = venues.map((venue) => ({
    id: venue.locationId,
    name: venue.locationName,
  }));

  // Update categories when location changes
  useEffect(() => {
    if (selectedLocation) {
      const selectedVenue = venues.find(
        (venue) => venue.locationId === selectedLocation
      );
      if (selectedVenue) {
        const venueCategories = selectedVenue.venueDetails.map(
          (detail) => detail.categoryName
        );
        setCategories(venueCategories);
      }
    } else {
      setCategories([]);
    }
    setSelectedCategory("");
  }, [selectedLocation, venues]);

  // Filter venues based on selected location and category
  const filteredVenues = venues
    .filter(
      (venue) => !selectedLocation || venue.locationId === selectedLocation
    )
    .flatMap((venue) =>
      venue.venueDetails
        .filter(
          (detail) =>
            !selectedCategory || detail.categoryName === selectedCategory
        )
        .flatMap((detail) =>
          detail.venueDetails.map((venueDetail) => ({
            ...venueDetail,
            locationName: venue.locationName,
            categoryName: detail.categoryName,
            locationId: venue.locationId,
            venueId: venueDetail._id,
            parentVenue: venue,
            venueLocationName: venueDetail.locationName,
          }))
        )
    );

  const handleEdit = (venue) => {
    const editData = {
      _id: venue.venueId,
      locationId: venue.locationId,
      locationName: venue.locationName,
      categoryName: venue.categoryName,
      venueLocationName: venue.venueLocationName,
      phoneNumber: venue.phoneNumber,
      price: venue.price,
      description: venue.description,
      amenities: Array.isArray(venue.amenities)
        ? venue.amenities
        : venue.amenities.split(","),
      images: venue.images,
      videos: venue.videos || (venue.video ? [venue.video] : []),
    };

    navigate(UPLOAD_VENUE_IMAGES, { state: { venue: editData } });
  };

  const handleDelete = async (venue) => {
    setVenueToDelete(venue);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      const deleteData = {
        locationId: venueToDelete.locationId,
        categoryName: venueToDelete.categoryName,
        venueDetailsId: venueToDelete.venueId,
      };

      const response = await axios.delete(
        `${
          import.meta.env.VITE_BACKEND_URL
        }/api/allVenues/delete-venue-details`,
        { data: deleteData }
      );

      if (response.data.success) {
        toast.success("Venue deleted successfully!");
        // Refresh the venues list
        const updatedResponse = await axios.get(
          `${
            import.meta.env.VITE_BACKEND_URL
          }/api/allVenues/get-all-venue-details`
        );
        if (updatedResponse.data.success) {
          setVenues(updatedResponse.data.data);
        }
      } else {
        throw new Error(response.data.message || "Failed to delete venue");
      }
    } catch (error) {
      console.error("Error deleting venue:", error);
      toast.error(
        `Failed to delete venue: ${
          error.response?.data?.message || error.message
        }`
      );
    } finally {
      setShowDeleteDialog(false);
      setVenueToDelete(null);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white rounded-2xl shadow-xl w-[1057px] mt-7 transform transition-all hover:shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-300 to-red-400 h-[109px] flex justify-start items-center px-8 rounded-t-2xl">
          <div className="flex items-center justify-center bg-white/90 text-red-800 rounded-full w-[200px] h-[60px] font-semibold text-sm text-left px-4 shadow-lg">
            All Venues
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          {/* Select Boxes */}
          <div className="flex justify-between gap-6 mb-8">
            <select
              value={selectedLocation}
              onChange={(e) => setSelectedLocation(e.target.value)}
              className="w-1/2 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
            >
              <option value="">All Locations</option>
              {locations.map((location) => (
                <option key={location.id} value={location.id}>
                  {location.name}
                </option>
              ))}
            </select>

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-1/2 p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
            >
              <option value="">All Categories</option>
              {categories.map((category, index) => (
                <option key={index} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Venue Cards */}
          <div className="flex flex-wrap justify-center gap-8">
            {filteredVenues.map((venue, index) => (
              <div
                key={index}
                className="w-[400px] h-[400px] border-2 border-gray-100 rounded-2xl p-4 shadow-lg bg-white hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col"
              >
                {/* Image Container */}
                <div className="relative w-full h-[300px] rounded-xl overflow-hidden group">
                  {venue.images && venue.images.length > 0 ? (
                    <>
                      <img
                        src={`${import.meta.env.VITE_BACKEND_URL}${
                          venue.images[0]
                        }`}
                        alt={venue.venueLocationName}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </>
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <div className="text-center">
                        <span className="text-gray-400 text-sm block">
                          No Image Available
                        </span>
                        <span className="text-gray-300 text-xs block mt-1">
                          Upload venue images
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 flex flex-col justify-between mt-4">
                  <div>
                    <div className="flex flex-row justify-between">
                      <h3 className="text-xl font-semibold text-gray-800 truncate">
                        {venue.venueLocationName}
                      </h3>

                      <span className="px-3 py-1 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full text-sm font-medium shadow-md backdrop-blur-sm bg-opacity-90">
                        ₹{venue.price}
                      </span>
                    </div>

                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-base text-gray-500">Category:</span>
                      <span className="text-base font-medium text-gray-700">
                        {venue.categoryName}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-base text-gray-500">Location:</span>
                      <span className="text-base font-medium text-gray-700 truncate">
                        {venue.locationName}
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 mt-4">
                    <button
                      onClick={() => handleEdit(venue)}
                      className="p-3 bg-gray-50 rounded-full hover:bg-gray-100 transition-all duration-200 shadow-sm hover:shadow-md"
                      title="Edit Venue"
                    >
                      <EditIcon
                        className="text-gray-600"
                        style={{ fontSize: "1.5rem" }}
                      />
                    </button>
                    <button
                      onClick={() => handleDelete(venue)}
                      className="p-3 bg-red-50 rounded-full hover:bg-red-100 transition-all duration-200 shadow-sm hover:shadow-md"
                      title="Delete Venue"
                    >
                      <DeleteIcon
                        className="text-red-600"
                        style={{ fontSize: "1.5rem" }}
                      />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl transform transition-all duration-300">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              Delete Venue
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this venue? This action cannot be
              undone.
            </p>
            <div className="flex justify-end gap-4">
              <button
                onClick={() => {
                  setShowDeleteDialog(false);
                  setVenueToDelete(null);
                }}
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-6 py-2 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full hover:from-red-500 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpdateVenue;

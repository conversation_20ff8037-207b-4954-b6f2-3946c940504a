import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { LOCATION } from "../../routes/Routes";
import axios from "axios";
import { toast } from 'react-hot-toast';

const AddVenue = () => {
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState("venue");
  const [locations, setLocations] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState("");
  const [venueName, setVenueName] = useState("");

  const handleGoToUploadImages = () => {
    setSelectedTab("location");
    navigate(LOCATION);
  };

  useEffect(() => {
    const getLocations = async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_BACKEND_URL}/api/locations/get-locations`
        );
        setLocations(response.data);
      } catch (error) {
        console.error("Error fetching locations:", error);
        toast.error("Failed to fetch locations");
      }
    };

    getLocations();
  }, []);

  const handleAddVenue = async (e) => {
    e.preventDefault();
    if (!venueName || !selectedLocation) {
      toast.error("Please fill all the fields");
      return;
    }
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/api/venues/add-venue`,
        {
          category: venueName,
          locationId: selectedLocation,
          locationName: locations.find(
            (place) => place._id === selectedLocation
          ).locationName,
        }
      );
      if (response.status === 200 || response.status === 201) {
        toast.success(response.data.message || "Venue added successfully");
        setVenueName("");
        setSelectedLocation("");
      }
    } catch (error) {
      console.error("Error adding venue:", error);
      toast.error(error.response?.data?.message || "Something went wrong");
    }
  };

  return (
    <div className="flex justify-center  min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="bg-white mt-16 rounded-2xl shadow-xl overflow-hidden w-[1057px] h-[659px] transform transition-all hover:shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-300 to-red-400 h-[109px] flex justify-start items-center px-8">
          <div className="flex items-center justify-center bg-white/90 text-red-800 rounded-full w-[200px] h-[60px] font-semibold text-sm text-left px-4 shadow-lg">
            Add Venue Category
          </div>
        </div>

        {/* Body */}
        <div className="px-8 py-8">
          {/* Tabs */}
          <div className="flex justify-center mb-8">
            <div className="flex rounded-2xl border-2 border-red-200 overflow-hidden shadow-inner">
              <button
                onClick={handleGoToUploadImages}
                className={`px-8 py-3 transition-all duration-300 ${
                  selectedTab === "location"
                    ? "bg-gradient-to-r from-red-400 to-red-500 text-white shadow-md"
                    : "bg-white text-red-600 hover:bg-red-50"
                }`}
              >
                Location Name
              </button>
              <button
                onClick={() => setSelectedTab("venue")}
                className={`px-8 py-3 transition-all duration-300 ${
                  selectedTab === "venue"
                    ? "bg-gradient-to-r from-red-400 to-red-500 text-white shadow-md"
                    : "bg-white text-red-600 hover:bg-red-50"
                }`}
              >
                Add Venue
              </button>
            </div>
          </div>

          {/* Form */}
          <form
            className="flex flex-col gap-6 items-start"
            onSubmit={handleAddVenue}
          >
            <div className="w-full">
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Location Name
              </label>
              <select
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
              >
                <option value="">Select a location</option>
                {locations.map((location) => (
                  <option key={location._id} value={location._id}>
                    {location.locationName}
                  </option>
                ))}
              </select>
            </div>

            <div className="w-full">
              <label className="block text-gray-800 font-semibold text-sm mb-2">
                Add Category
              </label>
              <input
                type="text"
                value={venueName}
                onChange={(e) => setVenueName(e.target.value)}
                placeholder="Enter category name"
                className="w-full p-4 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-red-300 focus:ring-2 focus:ring-red-100 transition-all duration-200"
              />
            </div>

            <div className="mt-4">
              <button
                type="submit"
                className="w-[200px] h-[50px] bg-gradient-to-r from-red-400 to-red-500 text-white rounded-full font-semibold text-sm hover:from-red-500 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Add Venue
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddVenue;
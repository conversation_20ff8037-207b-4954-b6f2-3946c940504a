import React from "react";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useNavigate } from "react-router-dom";
import {
  EVENY_LOCATION,
  UPLOAD_EVENT_IMAGES,
  UPDATE_EVENT,
} from "../routes/Routes";
const EventChanges = () => {
  const navigate = useNavigate();
  const redirect = (data) => {
    switch (data) {
      case "EVENY_LOCATION":
        return navigate(EVENY_LOCATION);
      case "UPLOAD_EVENT_IMAGES":
        return navigate(UPLOAD_EVENT_IMAGES);
      case "UPDATE_EVENT":
        return navigate(UPDATE_EVENT);
    }
  };
  return (
    <div className=" w-[100vw] h-[100vh] flex justify-center ">
      <div className="w-[80%] my-16 flex flex-col">
        <div className=" my-4 w-full h-fit grid grid-cols-2 md:grid-cols-1 ">
          <div
            onClick={() => redirect("EVENY_LOCATION")}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Add Location & Event Category</p>
            <ArrowForwardIosIcon />
          </div>

          <div
            onClick={() => redirect("UPLOAD_EVENT_IMAGES")}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Upload Event Images</p>
            <ArrowForwardIosIcon />
          </div>

          <div
            onClick={() => redirect("UPDATE_EVENT")}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Update Event</p>
            <ArrowForwardIosIcon />
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventChanges;

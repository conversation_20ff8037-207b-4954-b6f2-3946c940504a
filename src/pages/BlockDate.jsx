import React from 'react'
import <PERSON><PERSON>hartI<PERSON> from '@mui/icons-material/PieChart'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'
import { useNavigate } from 'react-router-dom'

import {
  ADMIN_INVOICE,
  ADMIN_INVOICE_CENTER,
  ALL_EXECUTIVE,
  ALL_FARMS,
  ALL_OCCASION,
  PREBOOK,
  PREBOOK_BY_DATERANGE,
  PREBOOK_PROPERTY,
  SUPER_ADMIN_INVOICE,
} from '../routes/Routes'
import NavTopBar from '../components/NavTopBar'
const BlockDate = () => {
  const navigate = useNavigate()
  const redirect = (data) => {
    switch (data) {
      case 'PreBook':
        return navigate(PREBOOK)
      case 'PreBookbyproperty':
        return navigate(PREBOOK_PROPERTY)
      case 'PreBookbyDateRange':
        return navigate(PREBOOK_BY_DATERANGE)
    }
  }
  return (
    <div className="  flex justify-center ">
      <div className="w-[100%] flex flex-col   ">
        <NavTopBar />
        
        <div className=" my-4 w-full h-fit grid grid-cols-2 md:grid-cols-1 ">

          <div
            onClick={() => redirect('PreBook')}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Block Date</p>
            <ArrowForwardIosIcon />
          </div>
          <div
            onClick={() => redirect('PreBookbyproperty')}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Block Date by property </p>
            <ArrowForwardIosIcon />
          </div>
          <div
            onClick={() => redirect('PreBookbyDateRange')}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Block Date by date range </p>
            <ArrowForwardIosIcon />
          </div>


        </div>
      </div>
    </div>
  )
}

export default BlockDate

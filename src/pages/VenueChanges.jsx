import React from "react";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useNavigate } from "react-router-dom";
import { LOCATION, UPDATE_VENUE, UPLOAD_VENUE_IMAGES } from "../routes/Routes";
const VenueChanges = () => {
  const navigate = useNavigate();
  const redirect = (data) => {
    switch (data) {
      case "LOCATION":
        return navigate(LOCATION);
      case "UPLOAD_VENUE_IMAGES":
        return navigate(UPLOAD_VENUE_IMAGES);
      case "UPDATE_VENUE":
        return navigate(UPDATE_VENUE);
    }
  };
  return (
    <div className=" w-[100vw] h-[100vh] flex justify-center ">
      <div className="w-[80%] my-16 flex flex-col">
        <div className=" my-4 w-full h-fit grid grid-cols-2 md:grid-cols-1 ">
          <div
            onClick={() => redirect("LOCATION")}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Add New Venue</p>
            <ArrowForwardIosIcon />
          </div>

          <div
            onClick={() => redirect("UPLOAD_VENUE_IMAGES")}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Upload Venue Details</p>
            <ArrowForwardIosIcon />
          </div>

          <div
            onClick={() => redirect("UPDATE_VENUE")}
            className=" hover:drop-shadow-md cursor-pointer m-4 rounded-xl flex justify-between items-center font-medium p-4 leading-[37.5px] bg-[#efefef]"
          >
            <p>Update Venue</p>
            <ArrowForwardIosIcon />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VenueChanges;

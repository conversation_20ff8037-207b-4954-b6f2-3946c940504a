/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        Primary:"#ed7e7e",
        Textgray:"#8d8d8d",
        Bo<PERSON><PERSON><PERSON>: '#eeeeee',
        Black: '#000000',
        
      },
    },

    screens: {
      '2xl': { max: '1535px' },
      //=>@media (max-width:1535px){...}
      xl: { max: '1279px' },
      //=>@media (max-width:1279px){...}
      lg: { max: '1023px' },
      //=>@media (max-width:1023px){...}
      md: { max: '767px' },
      //=>@media (max-width:767px){...}
      sm: { max: '639px' },
      //=>@media (max-width:639px){...}
      xs: { max: '479px' },
      //=>@media (max-width:479px){...}
    },
  },
  plugins: [],
}
